<?php
$test_groups = get_field( 'test_group', 'options' );
$modals = [];
$test_mode = false;

// Check if test_groups is an array and not empty
if ( is_array( $test_groups ) && !empty( $test_groups ) ) {
  foreach ( $test_groups as $test_group ) {
    // Check if test_group is an array before accessing its elements
    if ( is_array( $test_group ) && isset( $test_group['acf_fc_layout'] ) && $test_group['acf_fc_layout'] === 'form_modal' && $test_group['enabled'] ) {
      $modals = $test_group['Form Modal'];
      $test_mode = isset( $test_group['test_mode'] ) ? $test_group['test_mode'] : false;
      break;
    }
  }
}

// Check for URL parameter override
$url_test_mode = isset($_GET['modaltest']) && $_GET['modaltest'] == '1';
$is_test_mode = $test_mode || $url_test_mode;
?>
<?php if ( $modals ): ?>
<div class="form-modal-ab" id="form-modal-ab-variant-1"></div>
<div class="modal-variants" class="form-modal" style="display:none">
  <?php foreach ( $modals as $idx => $modal ): ?>
    <?php
    $desktop_layout = isset($modal['desktop_layout']) ? $modal['desktop_layout'] : (isset($modal['layout']) ? $modal['layout'] : 'two-column');
    $mobile_layout = isset($modal['mobile_layout']) ? $modal['mobile_layout'] : (isset($modal['layout']) ? $modal['layout'] : 'image-top');
    ?>
    <div id="modal-variant-<?= ($idx + 1); ?>" class="form-modal" data-desktop-layout="<?= $desktop_layout; ?>" data-mobile-layout="<?= $mobile_layout; ?>">

      <!-- Desktop Layout: One Column -->
      <div class="content one-column desktop-layout">
        <div class="image">
          <?= wp_get_attachment_image( $modal['image']['ID'], 'holiday_type_large' ); ?>
        </div>
        <?php if ($modal['hover_image']): ?>
        <div class="image hover">
          <?= wp_get_attachment_image( $modal['hover_image']['ID'], 'holiday_type_large' ); ?>
        </div>
        <?php endif; ?>
        <div class="content-overlay">
          <?= $modal['content']; ?>
          <?= do_shortcode('[gravityform id="' . $modal['gravity_forms'] .'" title="false" description="false" ajax="true"]'); ?>
        </div>
      </div>

      <!-- Desktop Layout: Two Column -->
      <div class="content two-column desktop-layout">
        <div class="row">
          <div class="col-md-6">
            <div class="image">
              <?= wp_get_attachment_image( $modal['image']['ID'], 'holiday_type_large' ); ?>
            </div>
          </div>
          <div class="col-md-6">
            <div class="content-inline">
              <?= $modal['content']; ?>
              <?= do_shortcode('[gravityform id="' . $modal['gravity_forms'] .'" title="false" description="false" ajax="true"]'); ?>
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile Layout: Image Top -->
      <div class="content image-top mobile-layout">
        <div class="image-section">
          <?= wp_get_attachment_image( $modal['image']['ID'], 'holiday_type_large' ); ?>
        </div>
        <div class="content-section">
          <?= $modal['content']; ?>
          <?= do_shortcode('[gravityform id="' . $modal['gravity_forms'] .'" title="false" description="false" ajax="true"]'); ?>
        </div>
      </div>

      <!-- Mobile Layout: Image Bottom -->
      <div class="content image-bottom mobile-layout">
        <div class="content-section">
          <?= $modal['content']; ?>
          <?= do_shortcode('[gravityform id="' . $modal['gravity_forms'] .'" title="false" description="false" ajax="true"]'); ?>
        </div>
        <div class="image-section">
          <?= wp_get_attachment_image( $modal['image']['ID'], 'holiday_type_large' ); ?>
        </div>
      </div>

    </div>
    </div>
  <?php endforeach; ?>
</div>

<script>
    (function($) {
        var isTestMode = <?= $is_test_mode ? 'true' : 'false'; ?>;

        $(function() {

            // Function to show appropriate layout based on screen size
            function updateModalLayout() {
                $('.form-modal').each(function() {
                    var $modal = $(this);
                    var desktopLayout = $modal.data('desktop-layout');
                    var mobileLayout = $modal.data('mobile-layout');
                    var isMobile = $(window).width() < 768;

                    // Remove active class from all layouts
                    $modal.find('.desktop-layout, .mobile-layout').removeClass('active');

                    if (isMobile) {
                        // Activate mobile layout
                        $modal.find('.mobile-layout.' + mobileLayout).addClass('active');
                    } else {
                        // Activate desktop layout
                        $modal.find('.desktop-layout.' + desktopLayout).addClass('active');
                    }
                });
            }

            // Update layout on page load and window resize
            updateModalLayout();
            $(window).on('resize', updateModalLayout);

            if (isTestMode || sessionStorage.getItem('modalWasShown') !== 'true') {
                var delay = isTestMode ? 1000 : 20000; // Show immediately in test mode (1 second delay for page load)
                setTimeout(function() {
                    var modal = detectModal();
                    if (modal) {
                        if (!isTestMode) {
                            sessionStorage.setItem('modalWasShown', 'true');
                        }
                        updateModalLayout(); // Ensure correct layout before opening
                        openModal(modal);
                    }
                }, delay);
            }

        });

        function detectModal() {
            // Detect whether Google Optimize has injected a div with an ID of
            // either variant-1 or variant-2 into the page
            var ab = $(".form-modal-ab");
            //form-modal-ab-variant-1
            if (ab.length) {
                return $("#modal-" + (ab.attr("id").replace('form-modal-ab-', '')));
            }
        }

        function openModal(modal) {
            $.fancybox.open( modal, {
                type: "content",
                hideOnOverlayClick: false,
                hideOnContentClick: false,
                afterClose: function() {
                    // In test mode, don't set the session storage so modal can reopen on page reload
                    if (!isTestMode) {
                        sessionStorage.setItem('modalWasShown', 'true');
                    }
                }
            } );
        }

    })(window.jQuery);
</script>
<?php endif; ?>
