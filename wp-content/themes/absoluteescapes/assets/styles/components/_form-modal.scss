.form-modal {

  padding: 0 !important;
  border-radius: 5px;

  .content {

    h2 {
      font-size: 3rem;
    }

    .image {

      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      background: #666;
      opacity: 1;
      transition: opacity 500ms;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &.hover {
        opacity: 0;
      }

    }

    &.one-column {
      position: relative;

      .content-overlay {
        position: relative;
        z-index: 1;
        padding: 66px 45px;
        text-align: center;
        color: #fff;

        * {
          color: #fff;
        }

      }

    }

    &.two-column {

      overflow: hidden;

      .content-inline {
        padding: 50px 15px;
      }

      // Mobile responsive: stack vertically on small screens
      @media (max-width: 767px) {
        .row {
          flex-direction: column;
        }

        .col-md-6 {
          width: 100%;
          max-width: 100%;
        }

        .image {
          position: relative;
          height: 200px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

    }

    &.image-top,
    &.image-bottom {

      .image-section {
        width: 100%;

        img {
          width: 100%;
          height: auto;
          max-height: 300px;
          object-fit: cover;
        }
      }

      .content-section {
        padding: 30px 20px;
        text-align: center;

        @media (min-width: 768px) {
          padding: 40px 30px;
        }
      }

    }

  }

  &:hover {

    .one-column .image {

      &:not(.hover) {
        opacity: 0;
      }

      &.hover {
        opacity: 1;
      }

    }

  }

  .gform_wrapper {

    .gform_required_legend {
      display: none;
    }

    .gfield_label {
      display: inline-block;
      font-size: 14px;
      font-weight: $font-weight-bold;
    }

    .gfield_required {
      display: none;
    }

    input[type='text'] {
      color: #000 !important;
      border-radius: 5px;
      height: 40px;
    }

    .button {
      border-radius: 5px;
      &:hover,
      &:active,
      &:focus {
        background-color: #008681;
      }
    }

    .ginput_container_checkbox {
      height: 100%;
    }

    .gfield_checkbox {

      display: flex;
      flex-wrap: wrap;
      height: 100%;
      align-items: stretch;

      .gchoice {

        background-color: #fff;
        display: flex;
        align-items: center;
        border-radius: 5px;
        padding: 0 8px;

        &:not(:last-child) {
          margin-right: 10px;
        }

      }

      label {

        color: #000 !important;
        padding-left: 0;
        padding-right: 25px;
        font-size: 12px;
        margin: 0;
        cursor: pointer;

        &:before {
          left: auto;
          right: 0;
          top: 1px;
        }

        &:after {
          left: auto;
          right: 4px;
          top: 2px;
        }
      }

    }

  }

  .one-column .gform_wrapper {

    .gform_fields {

      display: flex;
      margin: 0 -5px;
      flex-wrap: wrap;

      > .gfield {
        padding: 0 5px;
        flex-grow: 1;
        max-width: 100%;
      }

      .gfield_html {
        font-size: 13px;
        text-align: center;
        margin: 0;
      }

    }

    .gfield {
      text-align: left;
    }

  }

  .two-column .gform_wrapper {

    input[type='text'] {
      border-color: #000;
    }

    .gfield_checkbox {
      .gchoice {
        border: 1px solid #000;
        padding: 8px;
      }
    }

  }

  .image-top .gform_wrapper,
  .image-bottom .gform_wrapper {

    input[type='text'] {
      border-color: #000;
      margin-bottom: 15px;
    }

    .gfield_checkbox {
      .gchoice {
        border: 1px solid #000;
        padding: 8px;
        margin-bottom: 10px;
      }
    }

    .gform_fields {
      margin: 0;
    }

    .gfield {
      margin-bottom: 15px;
    }

  }

}
