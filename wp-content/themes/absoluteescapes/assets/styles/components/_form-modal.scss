.form-modal {

  padding: 0 !important;
  border-radius: 5px;

  // Ensure only one layout is visible at a time
  .desktop-layout,
  .mobile-layout {
    display: none !important;

    &.active {
      display: block !important;
    }
  }

  .content {

    h2 {
      font-size: 3rem;
    }

    .image {

      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      background: #666;
      opacity: 1;
      transition: opacity 500ms;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &.hover {
        opacity: 0;
      }

    }

    &.one-column {
      position: relative;

      .content-overlay {
        position: relative;
        z-index: 1;
        padding: 66px 45px;
        text-align: center;
        color: #fff;

        * {
          color: #fff;
        }

      }

    }

    &.two-column {

      overflow: hidden;
      position: relative;

      .content-inline {
        padding: 50px 15px;
      }



      // Mobile responsive: stack vertically on small screens
      @media (max-width: 767px) {
        .row {
          flex-direction: column;
        }

        .col-md-6 {
          width: 100%;
          max-width: 100%;
        }

        .image {
          position: relative;
          height: 200px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

    }

    &.image-top,
    &.image-bottom {

      .image-section {
        position: relative;
        width: 100%;
        height: 180px; // Reduced height for mobile to show more form content
        overflow: hidden;

        @media (min-width: 576px) {
          height: 220px; // Slightly taller on larger mobile
        }

        @media (min-width: 768px) {
          height: 300px; // Full height on desktop
        }

        &::before {
          content: "";
          width: 120px;
          height: 120px;
          position: absolute;
          top: 0;
          right: 0;
          z-index: 9;
          background-size: 100% 100%;
          background-position: 0px 0px;
          background-image: linear-gradient(45deg, #FFFFFF00 65%, #ffffffff 100%);

          @media (min-width: 768px) {
            display: none; // Only show on mobile
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center center;
        }
      }

      .content-section {
        padding: 20px 15px; // Reduced padding on mobile for more space
        text-align: center;
        background: #fff;

        @media (min-width: 576px) {
          padding: 25px 20px; // Slightly more padding on larger mobile
        }

        @media (min-width: 768px) {
          padding: 40px 30px; // Full padding on desktop
        }

        h2 {
          font-size: 2.5rem;
          margin-bottom: 10px; // Reduced from 20px for mobile
          color: #333;

          @media (min-width: 768px) {
            font-size: 3rem;
            margin-bottom: 20px; // Original spacing on desktop
          }
        }

        p {
          color: #666;
          font-size: 16px;
          line-height: 1.5;
          margin-bottom: 15px; // Reduced from 25px for mobile

          @media (min-width: 768px) {
            margin-bottom: 25px; // Original spacing on desktop
          }
        }
      }

    }

  }

  &:hover {

    .one-column .image {

      &:not(.hover) {
        opacity: 0;
      }

      &.hover {
        opacity: 1;
      }

    }

  }

  .gform_wrapper {

    .gform_required_legend {
      display: none;
    }

    .gfield_label {
      display: inline-block;
      font-size: 14px;
      font-weight: $font-weight-bold;
    }

    .gfield_required {
      display: none;
    }

    input[type='text'],
    input[type='email'] {
      color: #000 !important;
      border: 1px solid #ddd;
      border-radius: 5px;
      height: 40px;
      padding: 10px 15px;
      width: 100%;

      &:focus {
        border-color: #01a59f;
        outline: none;
      }
    }

    .button {
      border-radius: 5px;
      &:hover,
      &:active,
      &:focus {
        background-color: #008681;
      }
    }

    .ginput_container_checkbox {
      height: 100%;
    }

    .gfield_checkbox {

      display: flex;
      flex-wrap: wrap;
      height: 100%;
      align-items: stretch;

      .gchoice {

        background-color: #fff;
        display: flex;
        align-items: center;
        border-radius: 5px;
        padding: 0 8px;

        &:not(:last-child) {
          margin-right: 10px;
        }

      }

      label {

        color: #000 !important;
        padding-left: 0;
        padding-right: 25px;
        font-size: 12px;
        margin: 0;
        cursor: pointer;

        &:before {
          left: auto;
          right: 0;
          top: 1px;
        }

        &:after {
          left: auto;
          right: 4px;
          top: 2px;
        }
      }

    }

  }

  .one-column .gform_wrapper {

    .gform_fields {

      display: flex;
      margin: 0 -5px;
      flex-wrap: wrap;

      > .gfield {
        padding: 0 5px;
        flex-grow: 1;
        max-width: 100%;
      }

      .gfield_html {
        font-size: 13px;
        text-align: center;
        margin: 0;
      }

    }

    .gfield {
      text-align: left;
    }

  }

  .two-column .gform_wrapper {

    input[type='text'],
    input[type='email'] {
      border: 1px solid #ddd;

      &:focus {
        border-color: #01a59f;
      }
    }

    .gfield_checkbox {
      .gchoice {
        border: 1px solid #ddd;
        padding: 8px;
      }
    }

  }

  .image-top .gform_wrapper,
  .image-bottom .gform_wrapper {

    .gform_required_legend {
      display: none;
    }

    .gfield_label {
      display: inline-block;
      font-size: 14px;
      font-weight: $font-weight-bold;
      color: #333;
    }

    .gfield_required {
      display: none;
    }

    input[type='text'],
    input[type='email'] {
      color: #000 !important;
      border: 1px solid #ddd;
      border-radius: 5px;
      height: 40px;
      margin-bottom: 10px; // Reduced from 15px for mobile
      padding: 10px 15px;
      width: 100%;

      @media (min-width: 768px) {
        margin-bottom: 15px; // Original spacing on desktop
      }

      &:focus {
        border-color: #01a59f;
        outline: none;
      }
    }

    .button {
      background-color: #01a59f;
      border: none;
      border-radius: 5px;
      color: #fff;
      font-weight: normal; // Not bold on mobile
      padding: 10px 25px; // Reduced from 12px 30px for mobile
      cursor: pointer;

      @media (min-width: 768px) {
        font-weight: $font-weight-bold; // Bold on desktop
        padding: 12px 30px; // Original padding on desktop
      }

      &:hover,
      &:active,
      &:focus {
        background-color: #008681;
      }
    }

    .gfield_checkbox,
    .gfield_radio {
      height: 100%;

      .gchoice {
        background-color: #f8f8f8;
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 8px 12px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:not(:last-child) {
          margin-right: 10px;
        }

        &:hover {
          background-color: #e8e8e8;
          border-color: #01a59f;
        }

        // Hide the actual radio/checkbox input
        input[type="radio"],
        input[type="checkbox"] {
          position: absolute;
          opacity: 0;
          width: 0;
          height: 0;
          margin: 0;
          padding: 0;
        }
      }

      label {
        color: #333 !important;
        padding-left: 0;
        padding-right: 25px;
        font-size: 12px;
        margin: 0;
        cursor: pointer;
        flex: 1;
        position: relative;

        &:before {
          content: "";
          position: absolute;
          right: 0;
          top: 1px;
          width: 16px;
          height: 16px;
          border: 2px solid #ddd;
          background: #fff;
          transition: all 0.2s ease;
        }

        &:after {
          content: "";
          position: absolute;
          right: 4px;
          top: 5px;
          width: 8px;
          height: 8px;
          background: #01a59f;
          opacity: 0;
          transition: all 0.2s ease;
        }
      }

      // Checked state
      input[type="radio"]:checked + label,
      input[type="checkbox"]:checked + label {
        &:before {
          border-color: #01a59f;
        }

        &:after {
          opacity: 1;
        }
      }
    }

    // Radio button specific styling
    .gfield_radio {
      label {
        &:before {
          border-radius: 50%;
        }

        &:after {
          border-radius: 50%;
        }
      }
    }

    // Checkbox specific styling
    .gfield_checkbox {
      label {
        &:after {
          background: none;
          border: solid #01a59f;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
          width: 6px;
          height: 10px;
          right: 5px;
          top: 2px;
        }
      }
    }

    .gform_fields {
      margin: 0;

      > .gfield {
        margin-bottom: 10px; // Reduced from 15px for mobile

        @media (min-width: 768px) {
          margin-bottom: 15px; // Original spacing on desktop
        }

        &:last-child {
          margin-bottom: 2.5px; // Halved gap before submit button on mobile

          @media (min-width: 768px) {
            margin-bottom: 0; // No gap on desktop
          }
        }
      }
    }

    .gfield {
      text-align: left;
    }

  }



}
