$(window).on('load', function () {


    var $els = $(".subnav");

    if ($els.length) {
        Stickyfill.add($els);
    }

    var $enquiryForm = $('.enquiry-form');

    if ($enquiryForm.length) {
        Stickyfill.add($enquiryForm);
    }

    var getUrlParameter = function getUrlParameter(sParam) {
        var sPageURL = window.location.search.substring(1),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');

            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
            }
        }
    };

    var mastheadHeight = 115;

    function isAnyPartOfElementInViewport(el) {
        var rect = el.getBoundingClientRect();
        // DOMRect { x: 8, y: 8, width: 100, height: 100, top: 8, right: 108, bottom: 108, left: 8 }
        var windowHeight =
            window.innerHeight || document.documentElement.clientHeight;
        var windowWidth =
            window.innerWidth || document.documentElement.clientWidth;

        // http://stackoverflow.com/questions/325933/determine-whether-two-date-ranges-overlap
        var vertInView =
            rect.top <= windowHeight &&
            rect.top +
            (rect.height - (mastheadHeight + 1)) >=
            0;
        var horInView =
            rect.left <= windowWidth && rect.left + rect.width >= 0;

        return vertInView && horInView;

    }


    $(document).on('click', '.masthead a', function (e) {
        if (/#/.test(this.href) && !$(this).hasClass('aito-link') && !$(this).hasClass('modal-close')) {
            //if ($(this).is('[href*="#"]')) {
            e.preventDefault();
        }
    });

    $('.aito-link').on('click', function () {

        $('.review-bar').addClass('active');

    });

    $('.modal-close').on('click', function () {

        $('.review-bar').removeClass('active');

    });

    var $scrollNext = $('.scroll-next');

    $scrollNext.on('click', function () {
        $('html, body').animate({
            scrollTop: $(this).closest('section').next().offset().top - 50
        }, 500);
    });

var $scrollTo = $('.scroll-to');

$scrollTo.on('click', function () {
    // Get the target ID from data attribute
    var targetId = $(this).data('target');

    // If a target ID is specified, scroll to that element
    if (targetId) {
        var $targetElement = $('#' + targetId);
        if ($targetElement.length) {
            $('html, body').animate({
                scrollTop: $targetElement.offset().top - 50
            }, 500);
        }
    }
});



    var $backToTop = $('.back-to-top');

    $backToTop.on('click', function () {
        $('html, body').animate({
            scrollTop: $('html').offset().top
        }, 1000);
    });


    var $reviews = $('.reviews__row');

    $reviews.on('ready.flickity', function () {

        $(this).parent().find('.reviews__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.reviews__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $reviews.flickity({
        wrapAround: true,
        contain: true,
        freeScroll: true
    });


    // Improved text wrapping function with better break point logic
    function wrapTitleText(selector) {
        $(selector).each(function() {
            var $title = $(this);

            // Store original text and HTML to avoid repeated processing
            if (!$title.data('original-text')) {
                $title.data('original-text', $title.text().trim());
                $title.data('original-html', $title.html().trim());
            }

            var originalText = $title.data('original-text');
            var originalHtml = $title.data('original-html');

            // Reset to original content first to get accurate measurements
            $title.html(originalHtml);

            // Check if text is already on multiple lines by checking:
            // 1. Original HTML contains <br> tags
            // 2. Original text contains line breaks
            var hasExplicitLineBreaks = originalHtml.includes('<br>') ||
                                       originalHtml.includes('<BR>') ||
                                       originalText.includes('\n') ||
                                       originalText.includes('\r');

            if (hasExplicitLineBreaks) {
                // Text has explicit line breaks, don't apply wrap rule
                return;
            }

            var words = originalText.split(/\s+/);

            // Determine minimum word count based on panel configuration
            var minWords = getMinWordsForWrapping($title);

            // Only process if we have enough words
            if (words.length < minWords) {
                return;
            }

            // Find the best break point
            var breakPoint = findBestBreakPoint(words);

            if (breakPoint > 0 && breakPoint < words.length) {
                var firstLine = words.slice(0, breakPoint).join(' ');
                var secondLine = words.slice(breakPoint).join(' ');
                var wrappedText = firstLine + '<br>' + secondLine;
                $title.html(wrappedText);
            }
        });
    }

    // Function to determine minimum words needed for wrapping based on panel configuration
    function getMinWordsForWrapping($title) {
        // Check if this is a featured holidays panel title
        if ($title.hasClass('featured-holidays-panel__title')) {
            // Find the grid container and check data-count attribute
            var $grid = $title.closest('.featured-holidays-panel').find('.featured-holidays-panel__grid');
            if ($grid.length) {
                var count = parseInt($grid.attr('data-count')) || 0;
                // If 4 or more holidays, use original 3+ word rule
                if (count >= 4) {
                    return 3;
                }
            }
            // Default: require 4+ words for featured holidays panel
            return 4;
        }

        // For favourite holidays (carousel), always use original 3+ word rule
        if ($title.hasClass('favourite-holidays__title')) {
            return 3;
        }

        // Default fallback
        return 4;
    }

    // Function to find the best break point in a title
    function findBestBreakPoint(words) {
        var totalWords = words.length;

        // For 3 or 4 words, use original simple logic
        if (totalWords === 3 || totalWords === 4) {
            // Check for short words exception
            var hasShortWords = false;
            for (var i = 0; i < totalWords - 1; i++) {
                if (words[i].length < 4) {
                    hasShortWords = true;
                    break;
                }
            }

            if (hasShortWords) {
                return totalWords - 1; // Only last word on second line
            } else {
                return totalWords - 2; // Last 2 words on second line
            }
        }

        // For more than 4 words, avoid single word on second line
        // Look for good break points: before '&', after commas
        for (var i = 1; i < totalWords - 1; i++) {
            var word = words[i];
            var prevWord = words[i - 1];

            // Break before '&'
            if (word === '&' || word.startsWith('&')) {
                var wordsAfter = totalWords - i;
                if (wordsAfter >= 2) { // Ensure at least 2 words after break
                    return i;
                }
            }

            // Break after comma
            if (prevWord.endsWith(',')) {
                var wordsAfter = totalWords - i;
                if (wordsAfter >= 2) { // Ensure at least 2 words after break
                    return i;
                }
            }
        }

        // Default: ensure at least 2 words on second line
        var minWordsOnSecondLine = 2;
        var maxBreakPoint = totalWords - minWordsOnSecondLine;

        // Check for short words in first part
        var hasShortWords = false;
        for (var i = 0; i < maxBreakPoint; i++) {
            if (words[i].length < 4) {
                hasShortWords = true;
                break;
            }
        }

        if (hasShortWords && totalWords > 5) {
            // If there are short words and more than 5 total words,
            // be more conservative and put more words on second line
            return Math.max(totalWords - 3, Math.floor(totalWords / 2));
        } else {
            // Default: last 2 words on second line
            return maxBreakPoint;
        }
    }

    // Function to wrap title text if 3 or more words
    function wrapFeaturedHolidaysPanelTitles() {
        wrapTitleText('.featured-holidays-panel__title');
    }

    // Function to wrap title text if 3 or more words
    function wrapFavouriteHolidaysTitles() {
        wrapTitleText('.favourite-holidays__title');
    }

    // Unified function to handle all title wrapping
    function wrapAllTitles() {
        wrapTitleText('.featured-holidays-panel__title, .favourite-holidays__title');
    }





    // Function to equalize heights of favourite holidays content
    function equalizeFavouriteHolidaysHeights() {
        var $contents = $('.favourite-holidays__col-content');
        var $backgrounds = $('.favourite-holidays__background');
        var maxContentHeight = 0;

        // Reset heights first
        $backgrounds.css('height', '');
        $contents.css('min-height', '');

        // Find the tallest content
        $contents.each(function() {
            var contentHeight = $(this).outerHeight();
            if (contentHeight > maxContentHeight) {
                maxContentHeight = contentHeight;
            }
        });

        // Set minimum height for all content elements to match the tallest
        $contents.css('min-height', maxContentHeight + 'px');

        // Also ensure backgrounds have proper height
        var minBackgroundHeight = 445; // Minimum height from CSS
        var totalBackgroundHeight = Math.max(maxContentHeight + 40, minBackgroundHeight); // 40px for padding
        $backgrounds.css('height', totalBackgroundHeight + 'px');
    }

    var $favHolidays = $('.favourite-holidays__row');

    $favHolidays.on('ready.flickity', function () {

        $(this).parent().find('.favourite-holidays__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.favourite-holidays__button--next').appendTo($(this).find('.flickity-button.next'));

        // Wrap titles and equalize heights after slider is ready
        setTimeout(function() {
            // wrapFavouriteHolidaysTitles(); // Temporarily disabled
            // wrapFeaturedHolidaysPanelTitles(); // Temporarily disabled
            equalizeFavouriteHolidaysHeights();
        }, 100);

    });

    // Also apply wrapping and height equalization when slider changes
    $favHolidays.on('change.flickity', function() {
        setTimeout(function() {
            // wrapFavouriteHolidaysTitles(); // Temporarily disabled
            // wrapFeaturedHolidaysPanelTitles(); // Temporarily disabled
            equalizeFavouriteHolidaysHeights();
        }, 50);
    });

    $favHolidays.flickity({
        freeScroll: true,
        groupCells: true
    });

    // Debounce function to limit resize handler calls
    function debounce(func, wait) {
        var timeout;
        return function executedFunction() {
            var context = this;
            var args = arguments;
            var later = function() {
                timeout = null;
                func.apply(context, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Debounced function for resize handling
    var debouncedTitleWrappingResize = debounce(function() {
        // Handle all title wrapping
        // wrapAllTitles(); // Temporarily disabled

        // Handle favourite holidays height equalization
        if ($('.favourite-holidays__row').length) {
            equalizeFavouriteHolidaysHeights();
        }
    }, 250);

    // Also wrap titles and equalize heights on window resize (debounced)
    $(window).on('resize', debouncedTitleWrappingResize);


    var $destinations = $('.destinations__row');

    $destinations.on('ready.flickity', function () {

        $(this).parent().find('.favourite-holidays__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.favourite-holidays__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $destinations.flickity({
        wrapAround: true,
        pageDots: false,
        freeScroll: true,
        contain: true,
        groupCells: true,
        cellAlign: 'left'
    });


    var $photos = $('.instagram__photos');

    $photos.on('ready.flickity', function () {

        $(this).parent().find('.instagram__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.instagram__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $photos.flickity({
        pageDots: false,
        freeScroll: true,
        contain: true,
        groupCells: true,
        cellAlign: 'left'
    });

    var $carousel = $('.carousel__images');

    $carousel.on('ready.flickity', function () {

        $(this).parent().find('.carousel__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.carousel__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $carousel.flickity({
        wrapAround: true,
        contain: true,
        groupCells: true,
    });

    var $holidaysGallery = $('.holidays__gallery');

    $holidaysGallery.flickity({
        wrapAround: true,
        contain: true,
        groupCells: true,
        arrowShape: {
            x0: 10,
            x1: 60, y1: 50,
            x2: 70, y2: 35,
            x3: 35
        }
    });


    var $accommodationGallery = $('.accommodation__images');

    $accommodationGallery.on('ready.flickity', function () {

        $(this).parent().find('.accommodation__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.accommodation__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $accommodationGallery.flickity({
        wrapAround: true,
        contain: true,
        groupCells: true,
    });


    var $grid = $(".inf-posts");

    if ($('.next-posts-link').length && $grid.length) {
        $grid.infiniteScroll({
            path: ".next-posts-link a",
            append: ".inf-posts .inf-post",
            history: true,
            button: ".button-inf",
            scrollThreshold: false,
            status: ".page-load-status"
        });

        $grid.on("append.infiniteScroll", function (
            event,
            response,
            path,
            items
        ) {
            $(items).addClass("appended-item");
            $grid.imagesLoaded(function () {

                $(items)
                    .find("img")
                    .each(function (index, img) {
                        img.outerHTML = img.outerHTML;
                    });

                if ($(document).find('.holidays__gallery').length) {
                    $(document).find('.holidays__gallery').flickity({
                        wrapAround: true,
                        contain: true,
                        groupCells: true,
                        arrowShape: {
                            x0: 10,
                            x1: 60, y1: 50,
                            x2: 70, y2: 35,
                            x3: 35
                        }
                    });
                }

                // Re-initialize holiday row click handlers for newly loaded items
                initHolidayRowClickHandlers();
            });
        });
    }


    var rangeSlider = document.getElementById('distanceRangeSlider');

    if ($('#distanceRangeSlider').length) {
        noUiSlider.create(rangeSlider, {
            start: [1, 32],
            connect: true,
            step: 1,
            range: {
                'min': 1,
                'max': 32
            },
        });


        var minDuration = getUrlParameter('durationmin');
        var maxDuration = getUrlParameter('durationmax');

        if (minDuration && maxDuration) {
            rangeSlider.noUiSlider.set([minDuration, maxDuration]);
        } else if (minDuration) {
            rangeSlider.noUiSlider.set([minDuration, null]);
        } else if (maxDuration) {
            rangeSlider.noUiSlider.set([null, maxDuration]);
        }


        rangeSlider.noUiSlider.on('update', function (values, handle) {
            $('.filter__range-number--min').text(Math.floor(values[0]));
            $('#durationMin').val(Math.floor(values[0]));
            $('.filter__range-number--max').text(Math.floor(values[1]));
            $('#durationMax').val(Math.floor(values[1]));
        });

        $('#orderDropdown').on('change', function () {
            $('#sort').val($(this).val());
            $('#filterForm').submit();
        });

        rangeSlider.noUiSlider.on('change', function () {
            $('#filterForm').submit();
        });

    }

    $('.filter__input').on('change', function () {
        $('#filterForm').submit();
    });

    var dropdownMoving = false;

    $('.filter__label-wrapper').on('click', function () {

        var $this = $(this);

        if (!dropdownMoving) {
            dropdownMoving = true;

            if (!$this.hasClass('collapsed')) {
                $this.next().slideUp(function () {
                    $this.addClass('collapsed');
                    dropdownMoving = false;
                });
            } else {
                $this.next().slideDown(function () {
                    $this.removeClass('collapsed');
                    dropdownMoving = false;
                });

            }
        }
    });


    var images = [];
    var galleryObj;

    $('.page-header__gallery').find('img').each(function () {
        galleryObj = {}
        galleryObj['src'] = $(this).attr('src');
        images.push(galleryObj);

    });

    $('#galleryTrigger').on('click', function (e) {
        e.preventDefault();

        $.fancybox.open(images, {
            loop: true
        });
        $('[data-fancybox="gallery"]').fancybox({
          afterLoad : function(instance, current) {
            current.$image.attr('alt', current.opts.$orig.find('img').attr('alt') );
          }
        });
    });

    var overAccordionAnimating = false;

    $('.accordion__heading-wrapper').on('click', function (e) {
        var $this = $(this);

        // Don't open accordion if clicking on a button
        if ($(e.target).hasClass('button') || $(e.target).closest('.button').length > 0) {
            return;
        }

        if (!overAccordionAnimating) {
            overAccordionAnimating = true;

            if (!$this.parent().hasClass('active')) {
                $this.parent().addClass('active');
                $this.next().slideDown(function () {
                    overAccordionAnimating = false;
                });
            } else {
                $this.parent().removeClass('active');
                $this.next().slideUp(function () {
                    overAccordionAnimating = false;
                });
            }
        }

    });

    $('.accordion__close').on('click', function () {

        if (!overAccordionAnimating) {
            overAccordionAnimating = true;

            var $this = $(this);

            $this.parent().parent().slideUp(function () {
                overAccordionAnimating = false;

                $this.parent().parent().parent().removeClass('active');
            });

        }

    });

    // $('.subnav__link').on('click', function() {
    //     $('.subnav__item').removeClass('active');

    //     $(this).parent().addClass('active');

    // });

    $('#subNav').on('change', function () {
        var value = $(this).val();

        $('html, body').animate({
            scrollTop: $('#' + value).offset().top - 100
        }, 0);
    });


    if ($(".subnav").length) {
        setTimeout(function () {
            if ($(this).scrollTop() <= 150) {
                $('.subnav__link').parent().removeClass('active');
                $('#subNav').val('');
            } else {
                $("section").each(function () {
                    if ($(this).prop("id") !== "") {
                        var $that = $(this);
                        if (isAnyPartOfElementInViewport(this)) {
                            $(".subnav__link").each(function () {
                                if ($(this).attr("data-id") === $that.prop("id")) {
                                    $(
                                        ".subnav__link[data-id=" +
                                        $that.prop("id") +
                                        "]"
                                    )
                                        .parent()
                                        .addClass("active");

                                    $('#subNav').val($that.prop("id"));
                                } else {
                                    $(".subnav__link")
                                        .not(
                                            ".subnav__link[data-id=" +
                                            $that.prop("id") +
                                            "]"
                                        )
                                        .parent()
                                        .removeClass("active");

                                    $('#subNav').val('');
                                }
                            });

                            return false;
                        } else {
                            $(".subnav__link")
                                .not(
                                    ".subnav__link[data-id=" +
                                    $that.prop("id") +
                                    "]"
                                )
                                .parent()
                                .removeClass("active");

                            $('#subNav').val('');
                        }
                    }
                });
            }
        }, 50);


        $(window).on("scroll", function () {
            if ($(this).scrollTop() <= 150) {
                $('.subnav__link').parent().removeClass('active');
                $('#subNav').val('');
            } else {
                $("section").each(function () {
                    if ($(this).prop("id") !== "") {
                        var $that = $(this);
                        if (isAnyPartOfElementInViewport(this)) {
                            $(".subnav__link").each(function () {
                                if ($(this).attr("data-id") === $that.prop("id")) {
                                    $(
                                        ".subnav__link[data-id=" +
                                        $that.prop("id") +
                                        "]"
                                    )
                                        .parent()
                                        .addClass("active");

                                    $('#subNav').val($that.prop('id'));
                                } else {
                                    $(".subnav__link")
                                        .not(
                                            ".subnav__link[data-id=" +
                                            $that.prop("id") +
                                            "]"
                                        )
                                        .parent()
                                        .removeClass("active");


                                }
                            });

                            return false;
                        } else {
                            $(".subnav__link")
                                .not(
                                    ".subnav__link[data-id=" +
                                    $that.prop("id") +
                                    "]"
                                )
                                .parent()
                                .removeClass("active");

                            $('#subNav').val('');
                        }
                    }
                });
            }
        });
    }

    $('[data-toggle="datepicker"]').datepicker({
        format: 'dd/mm/yyyy',
        autoHide: true
    });


    $('.tooltip').on('click', function () {

        $(this).toggleClass('active');

    });

    $('.search-trigger').on('click', function () {

        $('.masthead__form-container').toggleClass('active');

        if ($('.masthead__form-container').hasClass('active')) {
            $('.masthead__form > input').focus();
        }

    });

    $('.masthead__form-close').on('click', function () {

        $('.masthead__form-container').removeClass('active');

    });

    var overviewCopyAnimating = false;

    $('.overview__bottom-copy-trigger').on('click', function () {

        if(!overviewCopyAnimating) {
            overviewCopyAnimating = true;
            if (!$(this).hasClass('active')) {
                $(this).addClass('active');
                $(this).next().slideDown(function () {
                    overviewCopyAnimating = false;
                });
            } else {
                $(this).removeClass('active');
                $(this).next().slideUp(function () {
                    overviewCopyAnimating = false;
                });
            }
        }

    });

    // Initialize banner slideshow
    if($('.banner__slideshow').length) {
        var $bannerSlideshow = $('.banner__slideshow');
        var slideCount = $bannerSlideshow.find('.banner__slide').length;

        console.log('Banner slideshow found. Slide count:', slideCount);

        // Only initialize Flickity if there are multiple slides
        if (slideCount > 1) {
            try {
                // Add fade enabled class
                $bannerSlideshow.addClass('fade-enabled');

                // Initialize Flickity with basic options
                $bannerSlideshow.flickity({
                    autoPlay: 6000,             // Auto-advance slides every 6 seconds
                    wrapAround: true,           // Loop slides
                    pauseAutoPlayOnHover: false, // Continue auto-play on hover
                    prevNextButtons: true,      // Enable navigation buttons
                    pageDots: false,            // Hide page dots
                    draggable: false,           // Disable dragging for auto-play slideshow
                    accessibility: false,       // Disable keyboard navigation
                    setGallerySize: false       // Don't set height based on tallest cell
                });

                console.log('Flickity initialized with autoPlay: 6000');

                // Wait for Flickity to be ready
                $bannerSlideshow.on('ready.flickity', function() {
                    // Replace Flickity's default SVG with our custom ones
                    var $prevButton = $bannerSlideshow.find('.flickity-button.previous');
                    var $nextButton = $bannerSlideshow.find('.flickity-button.next');

                    // Clear existing content and add our custom SVGs
                    $prevButton.empty().html('<svg class="svg-inline--fa banner-prev-arrow" aria-hidden="true" focusable="false" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" preserveAspectRatio="xMidYMid meet"><path fill="currentColor" d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path></svg>');

                    $nextButton.empty().html('<svg class="svg-inline--fa banner-next-arrow" aria-hidden="true" focusable="false" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" preserveAspectRatio="xMidYMid meet"><path fill="currentColor" d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path></svg>');

                    // Set up fade positioning
                    var $slides = $bannerSlideshow.find('.banner__slide');

                    // Reset all slides for fade effect, but preserve first slide visibility
                    $slides.each(function(index) {
                        var $slide = $(this);
                        $slide.css({
                            'position': 'absolute',
                            'top': '0',
                            'left': '0',
                            'width': '100%',
                            'height': '100%',
                            'transform': 'none'
                        });

                        // Only hide non-first slides
                        if (index > 0) {
                            $slide.css('opacity', '0');
                        }
                    });

                    // Show selected slide
                    var $selectedSlide = $slides.filter('.is-selected');
                    $selectedSlide.css('opacity', '1');

                    // Reset slider transform
                    $bannerSlideshow.find('.flickity-slider').css('transform', 'none');

                    // Animate text on first slide
                    var $heading = $selectedSlide.find('.banner__heading');
                    if ($heading.length) {
                        $heading.css({
                            'opacity': '0'
                        }).animate({
                            'opacity': '1'
                        }, 800);
                    }
                });

                // Handle slide changes with fade effect
                $bannerSlideshow.on('change.flickity', function() {
                    var $slides = $bannerSlideshow.find('.banner__slide');
                    var $selectedSlide = $slides.filter('.is-selected');

                    // Reset slider transform
                    $bannerSlideshow.find('.flickity-slider').css('transform', 'none');

                    // Reset all slide positions
                    $slides.css({
                        'position': 'absolute',
                        'top': '0',
                        'left': '0',
                        'width': '100%',
                        'height': '100%',
                        'opacity': '0',
                        'transform': 'none'
                    });

                    // Show selected slide
                    $selectedSlide.css('opacity', '1');

                    // Animate text
                    var $heading = $selectedSlide.find('.banner__heading');
                    if ($heading.length) {
                        $heading.css({
                            'opacity': '0'
                        });

                        setTimeout(function() {
                            $heading.animate({
                                'opacity': '1'
                            }, 600);
                        }, 200);
                    }
                });

                // Pause slideshow when tab loses focus
                // $(window).on('blur', function() {
                //     try {
                //         $bannerSlideshow.flickity('pausePlayer');
                //     } catch(e) {
                //         console.log('Error pausing slideshow:', e);
                //     }
                // });

                // Resume slideshow when tab gains focus
                // $(window).on('focus', function() {
                //     try {
                //         $bannerSlideshow.flickity('unpausePlayer');
                //     } catch(e) {
                //         console.log('Error resuming slideshow:', e);
                //     }
                // });

            } catch(error) {
                console.error('Error initializing Flickity:', error);
            }
        } else {
            // Single slide - just show it with animation
            var $singleSlide = $bannerSlideshow.find('.banner__slide');
            var $heading = $singleSlide.find('.banner__heading');

            if ($heading.length) {
                $heading.css({
                    'opacity': '0'
                });

                setTimeout(function() {
                    $heading.animate({
                        'opacity': '1'
                    }, 800);
                }, 500);
            }
        }
    }

    // Initialize holiday row click handlers
    function initHolidayRowClickHandlers() {
        $('.holidays__post-row').off('click.holidayRow').on('click.holidayRow', function(e) {
            // Don't trigger if clicking on gallery, existing links, or flickity buttons
            if ($(e.target).closest('.holidays__gallery, a, .flickity-button').length) {
                return;
            }

            // Find the holiday URL from the title link
            var $titleLink = $(this).find('.holidays__title').closest('a');
            if ($titleLink.length && $titleLink.attr('href')) {
                window.location.href = $titleLink.attr('href');
            }
        });
    }

    // Initialize on document ready
    initHolidayRowClickHandlers();

    // Initialize text wrapping for all holiday titles
    // wrapAllTitles(); // Temporarily disabled

    AOS.init({
        once: true
    });

    $('.overlay').fadeOut();

});
