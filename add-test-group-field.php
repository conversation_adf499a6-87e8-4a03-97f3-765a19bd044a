<?php
/**
 * Add Test Group Field to Theme Settings
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory
 * 2. Visit: yoursite.com/add-test-group-field.php
 * 3. Delete this file after running it
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if ACF is active
if (!function_exists('acf_add_local_field_group')) {
    die('ACF Pro is not active!');
}

// Add the field group
acf_add_local_field_group(array(
    'key' => 'group_test_group_modal',
    'title' => 'A/B Testing - Form Modal',
    'fields' => array(
        array(
            'key' => 'field_ab_testing_tab',
            'label' => 'A/B Testing',
            'name' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
        ),
        array(
            'key' => 'field_test_group',
            'label' => 'Test Group',
            'name' => 'test_group',
            'type' => 'flexible_content',
            'instructions' => 'Define the content (of a single type) to appear within the test group. Don\'t mix and match content types within a test group.',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'layouts' => array(
                'layout_form_modal' => array(
                    'key' => 'layout_form_modal',
                    'name' => 'form_modal',
                    'label' => 'Form Modal',
                    'display' => 'block',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_modal_enabled',
                            'label' => 'Enabled',
                            'name' => 'enabled',
                            'type' => 'true_false',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'message' => '',
                            'default_value' => 0,
                            'ui' => 1,
                            'ui_on_text' => '',
                            'ui_off_text' => '',
                        ),
                        array(
                            'key' => 'field_form_modals',
                            'label' => 'Form Modals',
                            'name' => 'Form Modal',
                            'type' => 'repeater',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'collapsed' => '',
                            'min' => 0,
                            'max' => 0,
                            'layout' => 'block',
                            'button_label' => 'Add Content to Test Group',
                            'sub_fields' => array(
                                array(
                                    'key' => 'field_modal_layout',
                                    'label' => 'Layout',
                                    'name' => 'layout',
                                    'type' => 'radio',
                                    'instructions' => '',
                                    'required' => 1,
                                    'conditional_logic' => 0,
                                    'wrapper' => array(
                                        'width' => '',
                                        'class' => '',
                                        'id' => '',
                                    ),
                                    'choices' => array(
                                        'one-column' => 'Single column (image as background)',
                                        'two-column' => 'Two columns (image on left)',
                                    ),
                                    'allow_null' => 0,
                                    'other_choice' => 0,
                                    'default_value' => '',
                                    'layout' => 'horizontal',
                                    'return_format' => 'value',
                                    'save_other_choice' => 0,
                                ),
                                array(
                                    'key' => 'field_modal_content',
                                    'label' => 'Content',
                                    'name' => 'content',
                                    'type' => 'wysiwyg',
                                    'instructions' => '',
                                    'required' => 1,
                                    'conditional_logic' => 0,
                                    'wrapper' => array(
                                        'width' => '',
                                        'class' => '',
                                        'id' => '',
                                    ),
                                    'default_value' => '',
                                    'tabs' => 'all',
                                    'toolbar' => 'full',
                                    'media_upload' => 1,
                                    'delay' => 0,
                                ),
                                array(
                                    'key' => 'field_modal_image',
                                    'label' => 'Image',
                                    'name' => 'image',
                                    'type' => 'image',
                                    'instructions' => '',
                                    'required' => 1,
                                    'conditional_logic' => 0,
                                    'wrapper' => array(
                                        'width' => '',
                                        'class' => '',
                                        'id' => '',
                                    ),
                                    'return_format' => 'array',
                                    'preview_size' => 'medium',
                                    'library' => 'all',
                                    'min_width' => '',
                                    'min_height' => '',
                                    'min_size' => '',
                                    'max_width' => '',
                                    'max_height' => '',
                                    'max_size' => '',
                                    'mime_types' => '',
                                ),
                                array(
                                    'key' => 'field_modal_hover_image',
                                    'label' => 'Hover image',
                                    'name' => 'hover_image',
                                    'type' => 'image',
                                    'instructions' => 'Image that appears when modal is hovered. Only applies to the one column layout.',
                                    'required' => 0,
                                    'conditional_logic' => 0,
                                    'wrapper' => array(
                                        'width' => '',
                                        'class' => '',
                                        'id' => '',
                                    ),
                                    'return_format' => 'array',
                                    'preview_size' => 'medium',
                                    'library' => 'all',
                                    'min_width' => '',
                                    'min_height' => '',
                                    'min_size' => '',
                                    'max_width' => '',
                                    'max_height' => '',
                                    'max_size' => '',
                                    'mime_types' => '',
                                ),
                                array(
                                    'key' => 'field_modal_gravity_form',
                                    'label' => 'Form',
                                    'name' => 'gravity_forms',
                                    'type' => 'select',
                                    'instructions' => '',
                                    'required' => 0,
                                    'conditional_logic' => 0,
                                    'wrapper' => array(
                                        'width' => '',
                                        'class' => '',
                                        'id' => '',
                                    ),
                                    'choices' => array(
                                        '5' => 'Make an Enquiry - General',
                                        '2' => 'Make an Enquiry - Specific Holiday',
                                        '1' => 'Newsletter - Footer Signup',
                                        '9' => 'Newsletter - Loyalty Club',
                                        '7' => 'Newsletter - Website Pop-Up',
                                        '8' => 'Newsletter modal form B (switched off in June 2023)',
                                        '3' => 'Payment Form',
                                        '11' => 'Test Form',
                                        '12' => 'Test Payment Form',
                                    ),
                                    'default_value' => array(),
                                    'allow_null' => 0,
                                    'multiple' => 0,
                                    'ui' => 0,
                                    'return_format' => 'value',
                                    'ajax' => 0,
                                    'placeholder' => '',
                                ),
                            ),
                        ),
                    ),
                    'min' => '',
                    'max' => '',
                ),
            ),
            'button_label' => 'Add Content',
            'min' => '',
            'max' => '',
        ),
    ),
    'location' => array(
        array(
            array(
                'param' => 'options_page',
                'operator' => '==',
                'value' => 'theme-settings',
            ),
        ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
    'hide_on_screen' => '',
    'active' => true,
    'description' => '',
));

echo '<h1>Test Group Field Added Successfully!</h1>';
echo '<p>The A/B Testing tab with Test Group field has been added to your Theme Settings.</p>';
echo '<p><strong>Next steps:</strong></p>';
echo '<ol>';
echo '<li>Go to <strong>Theme Settings</strong> in your WordPress admin</li>';
echo '<li>Look for the <strong>A/B Testing</strong> tab</li>';
echo '<li>Add a <strong>Form Modal</strong> layout</li>';
echo '<li>Set <strong>Enabled</strong> to "Yes"</li>';
echo '<li>Add your content with "A journey of discovery" title</li>';
echo '<li>Select form ID 7 for "Newsletter - Website Pop-Up"</li>';
echo '<li>Upload your images</li>';
echo '</ol>';
echo '<p><strong>Important:</strong> Delete this file after running it for security reasons.</p>';
?>
